export interface ClientContactInformationParams {
    mainEmail: string,
    mainPhoneNumber?: string,
    mainWhatsapp?: string,
    mainAddress?: string,
    representativeName?: string,
    billingEmail?: string,
    billingPhoneNumber?: string,
    billingWhatsapp?: string,
    billingAddress?: string,
    purchasesEmail?: string,
    purchasesPhoneNumber?: string,
    purchasesWhatsapp?: string,
    salesEmail?: string,
    salesPhoneNumber?: string,
    salesWhatsapp?: string,
    shippingAddress?: string,
}

export default class ClientContactInformation {
  mainEmail: string;

  mainPhoneNumber?: string;

  mainWhatsapp?: string;

  mainAddress?: string;

  representativeName?: string;

  billingEmail?: string;

  billingPhoneNumber?: string;

  billingWhatsapp?: string;

  billingAddress?: string;

  purchasesEmail?: string;

  purchasesPhoneNumber?: string;

  purchasesWhatsapp?: string;

  salesEmail?: string;

  salesPhoneNumber?: string;

  salesWhatsapp?: string;

  shippingAddress?: string;

  constructor(params: ClientContactInformationParams) {
    this.mainEmail = params.mainEmail;
    this.mainPhoneNumber = params.mainPhoneNumber;
    this.mainWhatsapp = params.mainWhatsapp;
    this.mainAddress = params.mainAddress;
    this.representativeName = params.representativeName;
    this.billingEmail = params.billingEmail;
    this.billingPhoneNumber = params.billingPhoneNumber;
    this.billingWhatsapp = params.billingWhatsapp;
    this.billingAddress = params.billingAddress;
    this.purchasesEmail = params.purchasesEmail;
    this.purchasesPhoneNumber = params.purchasesPhoneNumber;
    this.purchasesWhatsapp = params.purchasesWhatsapp;
    this.salesEmail = params.salesEmail;
    this.salesPhoneNumber = params.salesPhoneNumber;
    this.salesWhatsapp = params.salesWhatsapp;
    this.shippingAddress = params.shippingAddress;
  }

  // Setter methods that return new instances (immutable pattern)

  /**
   * Sets the main email and returns a new ClientContactInformation instance
   */
  setMainEmail(mainEmail: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      mainEmail,
    });
  }

  /**
   * Sets the main phone number and returns a new ClientContactInformation instance
   */
  setMainPhoneNumber(mainPhoneNumber?: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      mainPhoneNumber,
    });
  }

  /**
   * Sets the main WhatsApp and returns a new ClientContactInformation instance
   */
  setMainWhatsapp(mainWhatsapp?: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      mainWhatsapp,
    });
  }

  /**
   * Sets the main address and returns a new ClientContactInformation instance
   */
  setMainAddress(mainAddress?: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      mainAddress,
    });
  }

  /**
   * Sets the representative name and returns a new ClientContactInformation instance
   */
  setRepresentativeName(representativeName?: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      representativeName,
    });
  }

  /**
   * Sets the billing email and returns a new ClientContactInformation instance
   */
  setBillingEmail(billingEmail?: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      billingEmail,
    });
  }

  /**
   * Sets the billing phone number and returns a new ClientContactInformation instance
   */
  setBillingPhoneNumber(billingPhoneNumber?: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      billingPhoneNumber,
    });
  }

  /**
   * Sets the billing WhatsApp and returns a new ClientContactInformation instance
   */
  setBillingWhatsapp(billingWhatsapp?: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      billingWhatsapp,
    });
  }

  /**
   * Sets the billing address and returns a new ClientContactInformation instance
   */
  setBillingAddress(billingAddress?: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      billingAddress,
    });
  }

  /**
   * Sets the purchases email and returns a new ClientContactInformation instance
   */
  setPurchasesEmail(purchasesEmail?: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      purchasesEmail,
    });
  }

  /**
   * Sets the purchases phone number and returns a new ClientContactInformation instance
   */
  setPurchasesPhoneNumber(purchasesPhoneNumber?: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      purchasesPhoneNumber,
    });
  }

  /**
   * Sets the purchases WhatsApp and returns a new ClientContactInformation instance
   */
  setPurchasesWhatsapp(purchasesWhatsapp?: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      purchasesWhatsapp,
    });
  }

  /**
   * Sets the sales email and returns a new ClientContactInformation instance
   */
  setSalesEmail(salesEmail?: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      salesEmail,
    });
  }

  /**
   * Sets the sales phone number and returns a new ClientContactInformation instance
   */
  setSalesPhoneNumber(salesPhoneNumber?: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      salesPhoneNumber,
    });
  }

  /**
   * Sets the sales WhatsApp and returns a new ClientContactInformation instance
   */
  setSalesWhatsapp(salesWhatsapp?: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      salesWhatsapp,
    });
  }

  /**
   * Sets the shipping address and returns a new ClientContactInformation instance
   */
  setShippingAddress(shippingAddress?: string): ClientContactInformation {
    return new ClientContactInformation({
      ...this,
      shippingAddress,
    });
  }
}

// /**
//  * ClientContactInformation Value Object
//  *
//  * Represents the contact information for a client in the presentation subdomain.
//  * This value object encapsulates all contact-related data and ensures data integrity
//  * through domain validation rules.
//  *
//  * As a value object, instances are immutable - any changes require creating a new instance.
//  */

// export interface ClientContactInformationProps {
//   mainEmail: string;
//   mainPhoneNumber?: string;
//   mainWhatsapp?: string;
//   mainAddress?: string;
//   representativeName?: string;
//   billingEmail?: string;
//   billingPhoneNumber?: string;
//   billingWhatsapp?: string;
//   billingAddress?: string;
//   purchasesEmail?: string;
//   purchasesPhoneNumber?: string;
//   purchasesWhatsapp?: string;
//   salesEmail?: string;
//   salesPhoneNumber?: string;
//   salesWhatsapp?: string;
//   shippingAddress?: string;
// }

// export class ClientContactInformation {
//   private readonly props: ClientContactInformationProps;

//   constructor(props: ClientContactInformationProps) {
//     ClientContactInformation.validateProps(props);
//     this.props = { ...props };
//   }

//   private static validateProps(props: ClientContactInformationProps): void {
//     // Domain validation: mainEmail is required and must be valid
//     if (!props.mainEmail || props.mainEmail.trim() === '') {
//       throw new Error('El email principal es requerido para la información de contacto del cliente');
//     }

//     if (!ClientContactInformation.isValidEmail(props.mainEmail)) {
//       throw new Error('El email principal debe tener un formato válido');
//     }

//     // Validate other emails if provided
//     const emailFields = ['billingEmail', 'purchasesEmail', 'salesEmail'] as const;
//     emailFields.forEach((field) => {
//       const email = props[field];
//       if (email && !ClientContactInformation.isValidEmail(email)) {
//         throw new Error(`El campo ${field} debe tener un formato de email válido`);
//       }
//     });

//     // Validate phone numbers if provided
//     const phoneFields = [
//       'mainPhoneNumber', 'billingPhoneNumber', 'purchasesPhoneNumber',
//       'salesPhoneNumber', 'mainWhatsapp', 'billingWhatsapp',
//       'purchasesWhatsapp', 'salesWhatsapp',
//     ] as const;

//     phoneFields.forEach((field) => {
//       const phone = props[field];
//       if (phone && !ClientContactInformation.isValidPhoneNumber(phone)) {
//         throw new Error(`El campo ${field} debe tener un formato de teléfono válido`);
//       }
//     });

//     // Validate addresses if provided
//     const addressFields = ['mainAddress', 'billingAddress', 'shippingAddress'] as const;
//     addressFields.forEach((field) => {
//       const address = props[field];
//       if (address && address.trim().length < 5) {
//         throw new Error(`El campo ${field} debe tener al menos 5 caracteres`);
//       }
//     });

//     // Validate representative name if provided
//     if (props.representativeName && props.representativeName.trim().length < 2) {
//       throw new Error('El nombre del representante debe tener al menos 2 caracteres');
//     }
//   }

//   private static isValidEmail(email: string): boolean {
//     const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
//     return emailRegex.test(email);
//   }

//   private static isValidPhoneNumber(phone: string): boolean {
//     // Allow numbers with optional + prefix and spaces/dashes
//     const phoneRegex = /^\+?[\d\s\-()]+$/;
//     return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 7;
//   }

//   // Getters for accessing properties
//   get mainEmail(): string {
//     return this.props.mainEmail;
//   }

//   get mainPhoneNumber(): string | undefined {
//     return this.props.mainPhoneNumber;
//   }

//   get mainWhatsapp(): string | undefined {
//     return this.props.mainWhatsapp;
//   }

//   get mainAddress(): string | undefined {
//     return this.props.mainAddress;
//   }

//   get representativeName(): string | undefined {
//     return this.props.representativeName;
//   }

//   get billingEmail(): string | undefined {
//     return this.props.billingEmail;
//   }

//   get billingPhoneNumber(): string | undefined {
//     return this.props.billingPhoneNumber;
//   }

//   get billingWhatsapp(): string | undefined {
//     return this.props.billingWhatsapp;
//   }

//   get billingAddress(): string | undefined {
//     return this.props.billingAddress;
//   }

//   get purchasesEmail(): string | undefined {
//     return this.props.purchasesEmail;
//   }

//   get purchasesPhoneNumber(): string | undefined {
//     return this.props.purchasesPhoneNumber;
//   }

//   get purchasesWhatsapp(): string | undefined {
//     return this.props.purchasesWhatsapp;
//   }

//   get salesEmail(): string | undefined {
//     return this.props.salesEmail;
//   }

//   get salesPhoneNumber(): string | undefined {
//     return this.props.salesPhoneNumber;
//   }

//   get salesWhatsapp(): string | undefined {
//     return this.props.salesWhatsapp;
//   }

//   get shippingAddress(): string | undefined {
//     return this.props.shippingAddress;
//   }

//   // Method to create a new instance with updated properties
//   updateWith(updates: Partial<ClientContactInformationProps>): ClientContactInformation {
//     return new ClientContactInformation({
//       ...this.props,
//       ...updates,
//     });
//   }

//   // Method to convert to plain object (useful for serialization)
//   toPlainObject(): ClientContactInformationProps {
//     return { ...this.props };
//   }

//   // Equality comparison
//   equals(other: ClientContactInformation): boolean {
//     if (!(other instanceof ClientContactInformation)) {
//       return false;
//     }

//     return JSON.stringify(this.props) === JSON.stringify(other.props);
//   }
// }
