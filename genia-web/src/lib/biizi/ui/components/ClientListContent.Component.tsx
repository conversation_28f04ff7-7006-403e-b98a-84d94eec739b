import {
  Cards<PERSON>rid, CircleLoader, Column, Row,
} from '@pitsdepot/storybook';

import { Client } from '#/lib/biizi/domain/aggregates/Client/Client.Entity';
import { PaginatedListComponent } from '#/lib/biizi/ui/components/PaginatedList.Component';

import ClientCard from './ClientCard.Component';

// Generic client type that can be either Client or extended versions
type ClientDisplayData = Client & {
  extractedEmail?: string;
  extractedPhone?: string;
  extractedCity?: string;
};

export default function ClientListContent({
  loading,
  viewMode,
  filtered,
  paginatedTotalNumberOfItems,
  columns,
  mapper,
  tableCurrentPage,
  handleTablePageChange,
  cardsCurrentPage,
  handleCardsPageChange,
  itemsPerPageCards,
}: {
  loading: boolean;
  viewMode: 'cards' | 'table';
  filtered: ClientDisplayData[];
  paginatedTotalNumberOfItems: number;
  columns: Column[];
  mapper: (items: ClientDisplayData[]) => Row[];
  tableCurrentPage: number;
  handleTablePageChange: (page: number) => void;
  cardsCurrentPage: number;
  handleCardsPageChange: (page: number) => void;
  itemsPerPageCards: number;
}) {
  if (loading) return <CircleLoader />;

  if (viewMode === 'table') {
    return (
      <PaginatedListComponent
        title={'Clientes'}
        columns={columns}
        items={filtered}
        totalNumberOfItems={paginatedTotalNumberOfItems}
        mapper={mapper}
        showHeader={false}
        loading={loading}
        currentPage={tableCurrentPage}
        handlePageChange={handleTablePageChange}
      />
    );
  }

  return (
    <div>
      <CardsGrid
        loading={loading}
        items={filtered}
        renderItem={(c) => <ClientCard client={c} />}
        currentPage={cardsCurrentPage}
        onPageChange={handleCardsPageChange}
        totalItems={paginatedTotalNumberOfItems}
        itemsPerPage={itemsPerPageCards}
      />
    </div>
  );
}
